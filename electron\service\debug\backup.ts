import * as fs from "fs";
import * as path from "path";
import { paramService } from "./param";
import { reportService } from "./report";
import { deviceFileService } from "./devicefile";
import { getMainWindow } from "ee-core/electron";
import { sendMessageToUIByNotify } from "../../utils/iecUiUtils";
import { IEC_EVENT } from "../../data/debug/notify";
import {
  handleCustomResponse,
  handleInternalError,
  ERROR_CODES,
  ERROR_MESSAGES,
} from "../../data/debug/errorCodes";
import { FileReadTransferStatus } from "iec-upadrpc";
import { logger } from "ee-core/log";
import { CustomInfoService } from "./custominfo";
import { t } from "../../data/i18n/i18n";

let cancelFlag = false;

class BackupService {
  private uploadTaskIds: string[] = [];
  private customInfoService = new CustomInfoService();
  async oneKeyBackup({ backupRoot, types, head, taskId }) {
    this.uploadTaskIds = [];
    const mainWindow = getMainWindow();
    let total = types.length;
    let current = 0;
    try {
      // 1. 创建主目录
      if (!fs.existsSync(backupRoot)) {
        fs.mkdirSync(backupRoot, { recursive: true });
      }
      // 2. 逐类型处理
      for (const type of types) {
        if (cancelFlag) {
          sendMessageToUIByNotify(
            IEC_EVENT.BACKUP_NOTIFY,
            {
              type: type, // 用当前备份类型做唯一标识
              data: {
                step: t("backup.cancel"),
                percent: 0,
                status: "USER_CANCEL",
                errorMsg: "",
              },
              deviceId: head.id,
            },
            mainWindow
          );
          cancelFlag = false;
          return handleCustomResponse(
            ERROR_CODES.OPERATE_FAILED,
            t("backup.userCancel")
          );
        }
        let step = "";
        // 统一用英文type判断
        // 1. 开始processing
        sendMessageToUIByNotify(
          IEC_EVENT.BACKUP_NOTIFY,
          {
            type,
            data: {
              step: t("backup.start") + type,
              percent: 0,
              status: t("backup.processing"),
              errorMsg: "",
            },
            deviceId: head.id,
          },
          mainWindow
        );
        try {
          if (type === "paramValue") {
            step = t("backup.exportParam");
            const paramDir = path.join(backupRoot, t("backup.deviceParam"));
            if (!fs.existsSync(paramDir)) fs.mkdirSync(paramDir);
            const paramPath = path.join(paramDir, t("backup.paramExport"));
            await paramService.exportAllParam({
              head,
              data: { path: paramPath, grpName: "ALLSETTING_TABLE" },
            });
          }
          if (type === "faultInfo") {
            step = t("backup.exportReport");
            const reportDir = path.join(backupRoot, t("backup.deviceFault"));
            if (!fs.existsSync(reportDir)) fs.mkdirSync(reportDir);

            // 获取debug_info中fc为LG的所有报告节点
            const lgReports = await this.customInfoService.getLGReports(
              head.id
            );
            logger.info(
              `[BackupService] 找到 ${lgReports.length} 个LG报告节点`
            );

            // 计算总进度
            let finishedReports = 0;
            const totalReports = lgReports.length;

            for (const lgReport of lgReports) {
              if (cancelFlag) {
                sendMessageToUIByNotify(
                  IEC_EVENT.BACKUP_NOTIFY,
                  {
                    type: type,
                    data: {
                      step: t("backup.cancel"),
                      percent: 0,
                      status: "USER_CANCEL",
                      errorMsg: "",
                    },
                    deviceId: head.id,
                  },
                  mainWindow
                );
                cancelFlag = false;
                throw new Error(t("backup.userCancel"));
              }

              try {
                // 使用报告描述作为文件名，如果没有描述则使用名称
                const fileName = lgReport.desc || lgReport.name;
                const xlsxPath = path.join(reportDir, `${fileName}.xlsx`);
                logger.info(
                  `[BackupService] 导出报告: ${lgReport.name} (${fileName}), method: ${lgReport.method}`
                );

                // 更新进度 - 开始处理当前报告
                const currentPercent =
                  totalReports === 0
                    ? 100
                    : Math.floor((finishedReports / totalReports) * 100);
                sendMessageToUIByNotify(
                  IEC_EVENT.BACKUP_NOTIFY,
                  {
                    type: type,
                    data: {
                      step: `${t("backup.exportingReport")}(${finishedReports + 1}/${totalReports}): ${fileName}`,
                      percent: currentPercent,
                      status: t("backup.processing"),
                      errorMsg: "",
                    },
                    deviceId: head.id,
                  },
                  mainWindow
                );

                // 根据method获取对应的报告数据
                let reportData = [];

                // 根据不同的method调用不同的获取方法
                switch (lgReport.method) {
                  case "QueryHisEvtByTime":
                    const commonResult =
                      await reportService.getCommonReportList({
                        head,
                        data: {
                          type: lgReport.name,
                          startTime: "",
                          stopTime: "",
                          entryAfter: "",
                          orderBy: "DESC",
                        },
                      });
                    reportData = commonResult.data || [];
                    break;
                  case "QueryHisFaultByTime":
                    const groupResult = await reportService.getGroupReportList({
                      head,
                      data: {
                        startTime: "",
                        stopTime: "",
                        faultAfter: "",
                      },
                    });
                    reportData = groupResult.data || [];
                    break;
                  case "QueryOpReportByTime":
                    const operateResult =
                      await reportService.getOperateReportList({
                        head,
                        data: {
                          startTime: "",
                          stopTime: "",
                          entryAfter: "",
                        },
                      });
                    reportData = operateResult.data || [];
                    break;
                  case "QueryAuditLogByTime":
                    const auditResult =
                      await reportService.getAuditLogReportList({
                        head,
                        data: {
                          startTime: "",
                          stopTime: "",
                          entryAfter: "",
                        },
                      });
                    reportData = auditResult.data || [];
                    break;
                  default:
                    logger.warn(
                      `[BackupService] 未知的method: ${lgReport.method}`
                    );
                    continue;
                }

                // 根据method调用对应的导出报告方法
                await reportService.exportCommonReport({
                  head,
                  data: {
                    type: lgReport.name, // 使用报告名称作为type
                    method: lgReport.method,
                    path: xlsxPath,
                    items: reportData,
                  },
                });

                // 更新进度 - 完成当前报告
                finishedReports++;
                const finalPercent =
                  totalReports === 0
                    ? 100
                    : Math.floor((finishedReports / totalReports) * 100);
                sendMessageToUIByNotify(
                  IEC_EVENT.BACKUP_NOTIFY,
                  {
                    type: type,
                    data: {
                      step: `${t("backup.exportingReport")}(${finishedReports}/${totalReports}): ${fileName}`,
                      percent: finalPercent,
                      status: t("backup.processing"),
                      errorMsg: "",
                    },
                    deviceId: head.id,
                  },
                  mainWindow
                );

                logger.info(
                  `[BackupService] 报告 ${lgReport.name} (${fileName}) 导出成功`
                );
              } catch (err) {
                logger.error(
                  `[BackupService] 报告 ${lgReport.name} (${lgReport.desc || lgReport.name}) 导出失败:`,
                  err
                );
                // 继续处理其他报告，不中断整个备份流程
                finishedReports++;
                const errorPercent =
                  totalReports === 0
                    ? 100
                    : Math.floor((finishedReports / totalReports) * 100);
                sendMessageToUIByNotify(
                  IEC_EVENT.BACKUP_NOTIFY,
                  {
                    type: type,
                    data: {
                      step: `${t("backup.exportingReport")}(${finishedReports}/${totalReports}): ${lgReport.desc || lgReport.name}`,
                      percent: errorPercent,
                      status: t("backup.processing"),
                      errorMsg:
                        err instanceof Error ? err.message : String(err),
                    },
                    deviceId: head.id,
                  },
                  mainWindow
                );
              }
            }
          }
          if (type === "waveReport") {
            step = t("backup.exportWave");
            const waveDir = path.join(backupRoot, t("backup.deviceWave"));
            if (!fs.existsSync(waveDir)) fs.mkdirSync(waveDir);
            const waveFiles = await deviceFileService.getDeviceFile({
              head,
              data: { filePath: "/wave/comtrade" },
            });
            if (waveFiles && Array.isArray(waveFiles) && waveFiles.length > 0) {
              let finished = 0;
              const total = waveFiles.length;
              this.uploadTaskIds.push(taskId);
              await deviceFileService.uploadDeviceFile({
                head,
                data: {
                  savePath: waveDir,
                  fileItems: waveFiles,
                  taskid: taskId,
                  cb: (result) => {
                    // 只统计每个文件完成
                    if (
                      result.status ===
                      FileReadTransferStatus.SINGLE_FILE_FINISH
                    ) {
                      // 2为FileReadTransferStatus.FINISH
                      finished++;
                    }
                    const percent = Math.floor((finished / total) * 100);
                    sendMessageToUIByNotify(
                      IEC_EVENT.BACKUP_NOTIFY,
                      {
                        type,
                        data: {
                          step: `${step}(${finished}/${total})`,
                          percent,
                          status: result.status,
                          errorMsg: result.errorMsg || "",
                        },
                        deviceId: head.id,
                      },
                      mainWindow
                    );
                  },
                },
              });
            }
          }
          if (type === "cidConfigPrjLog") {
            if (cancelFlag) {
              sendMessageToUIByNotify(
                IEC_EVENT.BACKUP_NOTIFY,
                {
                  type: type,
                  data: {
                    step: t("backup.cancel"),
                    percent: 0,
                    status: "USER_CANCEL",
                    errorMsg: "",
                  },
                  deviceId: head.id,
                },
                mainWindow
              );
              cancelFlag = false;
              return handleCustomResponse(
                ERROR_CODES.OPERATE_FAILED,
                t("backup.userCancel")
              );
            }
            step = t("backup.exportConfig");
            const configDir = path.join(backupRoot, t("backup.deviceConfig"));
            if (!fs.existsSync(configDir)) fs.mkdirSync(configDir);
            // 统计三类文件总数
            // CCD/CID
            const ccdDir = path.join(configDir, "CID-CCD");
            if (!fs.existsSync(ccdDir)) fs.mkdirSync(ccdDir);
            const ccdFiles = await deviceFileService.getDeviceFile({
              head,
              data: { filePath: "/shr/configuration" },
            });
            const ccdList =
              ccdFiles && Array.isArray(ccdFiles)
                ? ccdFiles.filter((f) => f.fileName.endsWith(".ccd"))
                : [];
            const cidList =
              ccdFiles && Array.isArray(ccdFiles)
                ? ccdFiles.filter((f) => f.fileName.endsWith(".cid"))
                : [];
            const allList = [...ccdList, ...cidList];
            // XML配置
            const xmlDir = path.join(configDir, t("backup.xmlConfig"));
            if (!fs.existsSync(xmlDir)) fs.mkdirSync(xmlDir);
            const xmlFiles = await deviceFileService.getDeviceFile({
              head,
              data: { filePath: "/shr" },
            });
            const xmlList =
              xmlFiles && Array.isArray(xmlFiles)
                ? xmlFiles.filter((f) => f.fileName.endsWith(".xml"))
                : [];
            // 日志文件
            const logDir = path.join(configDir, t("backup.logFiles"));
            if (!fs.existsSync(logDir)) fs.mkdirSync(logDir);
            const logFiles = await deviceFileService.getDeviceFile({
              head,
              data: { filePath: "/wave/log" },
            });
            const logList = logFiles && Array.isArray(logFiles) ? logFiles : [];
            // 全部文件总数
            const total = allList.length + xmlList.length + logList.length;
            let finished = 0;
            // 定义统一的进度回调
            const updateProgress = (subStep, errorMsg = "") => {
              const percent =
                total === 0 ? 100 : Math.floor((finished / total) * 100);
              sendMessageToUIByNotify(
                IEC_EVENT.BACKUP_NOTIFY,
                {
                  type, // 只传递原始 type
                  data: {
                    step: `${subStep}(${finished}/${total})`,
                    percent,
                    status: t("backup.processing"),
                    errorMsg: errorMsg || "",
                  },
                  deviceId: head.id,
                },
                mainWindow
              );
            };
            // CCD/CID
            if (allList.length) {
              if (cancelFlag) {
                sendMessageToUIByNotify(
                  IEC_EVENT.BACKUP_NOTIFY,
                  {
                    type: type,
                    data: {
                      step: t("backup.cancel"),
                      percent: 0,
                      status: "USER_CANCEL",
                      errorMsg: "",
                    },
                    deviceId: head.id,
                  },
                  mainWindow
                );
                cancelFlag = false;
                return handleCustomResponse(
                  ERROR_CODES.OPERATE_FAILED,
                  t("backup.userCancel")
                );
              }
              this.uploadTaskIds.push(taskId);
              await deviceFileService.uploadDeviceFile({
                head,
                data: {
                  savePath: ccdDir,
                  fileItems: allList,
                  taskid: taskId,
                  cb: (result) => {
                    if (cancelFlag) return; // 取消后不再更新进度
                    logger.info("result:", result);
                    if (
                      result.status ===
                      FileReadTransferStatus.SINGLE_FILE_FINISH
                    ) {
                      finished++;
                      updateProgress("CCD+CID", result.errorMsg);
                    }
                  },
                },
              });
            }
            // XML配置
            if (xmlList.length) {
              if (cancelFlag) {
                sendMessageToUIByNotify(
                  IEC_EVENT.BACKUP_NOTIFY,
                  {
                    type: type,
                    data: {
                      step: t("backup.cancel"),
                      percent: 0,
                      status: "USER_CANCEL",
                      errorMsg: "",
                    },
                    deviceId: head.id,
                  },
                  mainWindow
                );
                cancelFlag = false;
                return handleCustomResponse(
                  ERROR_CODES.OPERATE_FAILED,
                  t("backup.userCancel")
                );
              }
              this.uploadTaskIds.push(taskId);
              await deviceFileService.uploadDeviceFile({
                head,
                data: {
                  savePath: xmlDir,
                  fileItems: xmlList,
                  taskid: taskId,
                  cb: (result) => {
                    if (cancelFlag) return;
                    if (
                      result.status ===
                      FileReadTransferStatus.SINGLE_FILE_FINISH
                    ) {
                      finished++;
                      updateProgress("XML", result.errorMsg);
                    }
                  },
                },
              });
            }
            // 日志文件
            if (logList.length) {
              if (cancelFlag) {
                sendMessageToUIByNotify(
                  IEC_EVENT.BACKUP_NOTIFY,
                  {
                    type: type,
                    data: {
                      step: t("backup.cancel"),
                      percent: 0,
                      status: "USER_CANCEL",
                      errorMsg: "",
                    },
                    deviceId: head.id,
                  },
                  mainWindow
                );
                cancelFlag = false;
                return handleCustomResponse(
                  ERROR_CODES.OPERATE_FAILED,
                  t("backup.userCancel")
                );
              }
              this.uploadTaskIds.push(taskId);
              await deviceFileService.uploadDeviceFile({
                head,
                data: {
                  savePath: logDir,
                  fileItems: logList,
                  taskid: taskId,
                  cb: (result) => {
                    if (cancelFlag) return;
                    if (
                      result.status ===
                      FileReadTransferStatus.SINGLE_FILE_FINISH
                    ) {
                      finished++;
                      updateProgress(t("backup.logs"), result.errorMsg);
                    }
                  },
                },
              });
            }
          }
          if (cancelFlag) {
            // 成功
            sendMessageToUIByNotify(
              IEC_EVENT.BACKUP_NOTIFY,
              {
                type: type, // 用当前备份类型做唯一标识
                data: {
                  step,
                  percent: 0,
                  status: "USER_CANCEL",
                  errorMsg: t("backup.userCancelShort"),
                },
                deviceId: head.id,
              },
              mainWindow
            );
          } else {
            // 成功
            sendMessageToUIByNotify(
              IEC_EVENT.BACKUP_NOTIFY,
              {
                type: type, // 用当前备份类型做唯一标识
                data: {
                  step,
                  percent: 100,
                  status: t("backup.success"),
                  errorMsg: "",
                },
                deviceId: head.id,
              },
              mainWindow
            );
          }
        } catch (err) {
          // 失败
          sendMessageToUIByNotify(
            IEC_EVENT.BACKUP_NOTIFY,
            {
              type: type,
              data: {
                step,
                percent: 100,
                status: t("backup.failed"),
                errorMsg: err instanceof Error ? err.message : String(err),
              },
              deviceId: head.id,
            },
            mainWindow
          );
          throw err;
        }
      }
      if (cancelFlag) {
        sendMessageToUIByNotify(
          IEC_EVENT.BACKUP_NOTIFY,
          {
            type: "cancelbackup",
            data: {
              step: t("backup.userCancelShort"),
              percent: 0,
              status: "USER_CANCEL",
              errorMsg: "",
            },
            deviceId: head.id,
          },
          mainWindow
        );
      } else {
        sendMessageToUIByNotify(
          IEC_EVENT.BACKUP_NOTIFY,
          {
            type: "backup",
            data: {
              step: t("backup.complete"),
              percent: 100,
              status: t("backup.complete"),
              errorMsg: "",
            },
            deviceId: head.id,
          },
          mainWindow
        );
      }
      cancelFlag = false;
      return handleCustomResponse(ERROR_CODES.SUCCESS, ERROR_MESSAGES.SUCCESS);
    } catch (err) {
      const error = err as Error;
      sendMessageToUIByNotify(
        IEC_EVENT.BACKUP_NOTIFY,
        {
          type: "backup",
          data: {
            step: t("backup.exception"),
            percent: 100,
            status: t("backup.failed"),
            errorMsg: error.message || String(error),
          },
          deviceId: head.id,
        },
        mainWindow
      );
      cancelFlag = false;
      return handleInternalError(error.message || String(error));
    }
  }
  async cancelBackup(head, data) {
    cancelFlag = true;
    let taskids = this.uploadTaskIds;
    if (data && data.id) {
      taskids = [data.id];
    }
    if (taskids && taskids.length > 0) {
      try {
        await deviceFileService.cancelUploadDeviceFIle({
          head,
          data: { taskids },
        });
      } catch (e) {
        // 忽略异常
        console.error(t("backup.cancelUploadError"), e);
      }
      this.uploadTaskIds = [];
    }
    return handleCustomResponse(ERROR_CODES.SUCCESS, ERROR_MESSAGES.SUCCESS);
  }
}

const backupService = new BackupService();
export { backupService };
