/**
 * 服务相关 - 中文
 */
export default {
  // 许可证服务
  license: {
    cacheResult: "使用缓存结果",
    verificationTime: "验证耗时",
    verificationFailed: "验证失败，耗时",
    businessErrorInfo: "获取业务错误信息",
    getMachineCode: "获取机器码",
    checkAuth: "检查授权",
    activate: "激活许可证",
  },

  // 跨平台服务
  cross: {
    startGoService: "开始启动Go服务...",
    goServiceStartSuccess: "Go服务启动成功，耗时",
    goServiceStartFailed: "Go服务启动失败，耗时",
    startPythonService: "开始启动Python服务...",
    pythonServiceStartSuccess: "Python服务启动成功，耗时",
    pythonServiceStartFailed: "Python服务启动失败，耗时",
    optimizeStartParams: "优化启动参数",
  },

  // 基础服务
  base: {
    getClientStart: "开始获取设备客户端，设备ID",
    deviceNotFound: "未找到设备信息，设备ID",
    deviceNotConnected: "设备未连接或客户端无效，设备ID",
    getClientSuccess: "成功获取设备客户端，设备ID",
  },

  // 设备连接服务
  deviceConnect: {
    deviceConnected: "设备已连接",
    connectionAttempt: "连接尝试",
    clientObtained: "客户端已获取，设备ID",
    clientNotObtained: "未获取到客户端，设备ID",
    connectionException: "连接异常",
    connectionCheckException: "连接检查异常",
    connectDeviceByRpc: "通过RPC连接设备",
    callInterface: "调取接口",
    useDeviceIdAsKey: "使用装置的id作为key",
    getConnectionFromGlobal: "从全局变量中获取连接对象",
    notifyFrontendConnectionStatus: "通知前端修改连接状态",
    retryInterval: "可选：每次重试间隔一段时间",
    cacheSuccessfulConnection: "将连接成功的连接对象缓存",
    resetData: "复位数据",
    compareMd5AndSetFilePath: "比较md5码，然后设置文件位置",
    addDebugInfoToGlobal:
      "往realSingleGlobalDeviceInfo添加debugInfo 和debugItemMap",
    connectionSuccessful: "连接成功，设备ID",
    disconnectDevice: "断开连接",
    getConnection: "获取连接",
    deleteConnectionObject: "删除连接对象",
    disconnectSuccessful: "断开连接成功，设备ID",
    disconnectException: "断开连接异常",
  },

  // 设备信息服务
  deviceInfo: {
    getDeviceInfoStart: "开始获取设备信息",
    exportStart: "开始导出设备信息",
    exportSuccess: "导出设备信息成功",
  },

  // 组态服务
  configure: {
    getConfigureList: "获取组态列表",
    addConfigure: "添加组态",
    setId: "设置ID",
    projectNotExists: "项目不存在",
    duplicateName: "名称重复，请重新输入",
    addConfigureException: "添加组态异常",
    projectNotFound: "未找到项目",
    projectPathNotFound: "未找到项目路径",
    replaceWithNew: "替换为新内容",
    projectNotFoundShort: "项目未找到",
    operationTypeIncorrect: "操作类型不正确，允许的值为[project,hmi]",
    renameConfigureException: "重命名组态异常",
    getConfigureListException: "获取组态列表异常",
    configureSaveException: "组态保存异常",
    openConfigureFolder: "打开组态文件夹",
  },

  // 窗口服务
  window: {
    windowStateInitialized: "窗口状态管理已初始化",
    windowStateInitFailed: "窗口状态管理初始化失败",
    windowStateSaved: "窗口状态已保存",
    saveWindowStateFailed: "保存窗口状态失败",
    windowStateRestored: "窗口状态已恢复",
    restoreWindowStateFailed: "恢复窗口状态失败",
    clickNotification: "点击通知",
    closeNotification: "关闭通知",
    createWindow: "创建窗口",
    windowCreated: "窗口已创建",
    windowCreationFailed: "窗口创建失败",
    getWindowId: "获取窗口ID",
    windowCommunication: "窗口间通信",
    notificationCreated: "通知已创建",
    windowClosed: "窗口已关闭",
    windowMaximized: "窗口已最大化",
    windowMinimized: "窗口已最小化",
    windowDragged: "窗口拖拽完成",
    screenshotTaken: "截图已保存",
    devToolsOpened: "开发者工具已打开",
    devToolsClosed: "开发者工具已关闭",
  },

  // 系统事件服务
  systemEvents: {
    systemEventsInitialized: "系统事件监听已初始化",
    eventListenersSet: "系统事件监听器已设置",
    setupEventListenersFailed: "设置事件监听器失败",
    systemSuspending: "系统即将休眠",
    systemResuming: "系统已唤醒",
    screenLocked: "屏幕已锁定",
    screenUnlocked: "屏幕已解锁",
    systemShuttingDown: "系统即将关机",
    appBeforeQuit: "应用即将退出",
    allWindowsClosed: "所有窗口已关闭",
    appActivated: "应用已激活",
    windowBlurred: "窗口失去焦点",
    windowFocused: "窗口获得焦点",
    windowMinimized: "窗口已最小化",
    windowRestored: "窗口已恢复",
    applicationStateSaved: "应用状态已保存",
    applicationStateRestored: "应用状态已恢复",
    powerInfoRetrieved: "电源信息已获取",
    cleanupCompleted: "清理完成",
  },

  // 托盘服务
  tray: {
    trayLoaded: "托盘已加载",
    trayCreated: "托盘已创建",
    trayCreationFailed: "托盘创建失败",
  },

  // 数据库服务
  database: {
    databaseConnected: "数据库已连接",
    databaseConnectionFailed: "数据库连接失败",
    queryExecuted: "查询已执行",
    queryFailed: "查询失败",
    dataInserted: "数据已插入",
    dataUpdated: "数据已更新",
    dataDeleted: "数据已删除",
    transactionStarted: "事务已开始",
    transactionCommitted: "事务已提交",
    transactionRolledBack: "事务已回滚",
    tableCreated: "表已创建",
    tableCreationFailed: "表创建失败",
    dataInsertFailed: "数据插入失败",
    dataDeleteFailed: "数据删除失败",
    dataUpdateFailed: "数据更新失败",
    dataDirRetrieved: "数据目录已获取",
    dataDirRetrieveFailed: "数据目录获取失败",
    customDataDirSet: "自定义数据目录已设置",
    customDataDirSetFailed: "自定义数据目录设置失败",
  },

  // HMI服务
  hmi: {
    graphDefined: "图形已定义",
    configurationLoaded: "配置已加载",
    viewCreated: "视图已创建",
    projectOpened: "项目已打开",
    projectSaved: "项目已保存",
    elementAdded: "元素已添加",
    elementModified: "元素已修改",
    elementDeleted: "元素已删除",
  },

  // 作业服务
  job: {
    jobStarted: "作业已开始",
    jobCompleted: "作业已完成",
    jobFailed: "作业失败",
    jobCancelled: "作业已取消",
    jobPaused: "作业已暂停",
    jobResumed: "作业已恢复",
    jobScheduled: "作业已调度",
    jobRemoved: "作业已移除",
  },

  // 矩阵服务
  matrix: {
    matrixInitialized: "矩阵已初始化",
    matrixCalculationStarted: "矩阵计算已开始",
    matrixCalculationCompleted: "矩阵计算已完成",
    matrixOperationFailed: "矩阵操作失败",
    dataProcessed: "数据处理完成",
    algorithmExecuted: "算法已执行",
    encryptCompressionComplete: "加密压缩完成",

    // 导出相关
    exportDeviceListEntry: "导出装置列表入口",
    exportDeviceListStart: "开始导出装置列表",
    exportDeviceListComplete: "装置列表导出完成",
    deviceListSheetName: "装置列表",
    exportDownloadListStart: "开始导出下载列表",
    exportParamListStart: "开始导出参数列表",

    // 表头翻译
    indexHeader: "序号",
    deviceNameHeader: "装置名称",
    deviceAddressHeader: "装置地址",
    devicePortHeader: "装置端口",
    encryptedHeader: "是否加密",
    downFileHeader: "下载文件",
    importParamHeader: "导入参数",
    connectTimeoutHeader: "连接超时",
    paramTimeoutHeader: "参数超时",
    readTimeoutHeader: "读取超时",
    fileNameHeader: "文件名",
    fileSizeHeader: "文件大小",
    filePathHeader: "文件路径",
    lastModifiedHeader: "最后修改时间",
    paramNameHeader: "参数名",
    paramDescHeader: "参数描述",
    valueHeader: "值",
    minValueHeader: "最小值",
    maxValueHeader: "最大值",
    stepHeader: "步长",
    unitHeader: "单位",
  },
};
